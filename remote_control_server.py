#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
برنامج التحكم عن بُعد - الخادم
Remote Control Server
"""

import socket
import threading
import json
import time
from datetime import datetime

class RemoteControlServer:
    def __init__(self, host="0.0.0.0", port=16067):
        self.host = host
        self.port = port
        self.server_socket = None
        self.clients = {}
        self.running = False
        
    def log_message(self, message):
        """تسجيل الرسائل مع الوقت"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] {message}")
        
    def start_server(self):
        """بدء الخادم"""
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind((self.host, self.port))
            self.server_socket.listen(5)
            
            self.running = True
            self.log_message(f"الخادم يعمل على {self.host}:{self.port}")
            self.log_message("في انتظار الاتصالات...")
            
            while self.running:
                try:
                    client_socket, client_address = self.server_socket.accept()
                    self.log_message(f"اتصال جديد من: {client_address}")
                    
                    # إنشاء thread جديد للعميل
                    client_thread = threading.Thread(
                        target=self.handle_client,
                        args=(client_socket, client_address)
                    )
                    client_thread.daemon = True
                    client_thread.start()
                    
                except Exception as e:
                    if self.running:
                        self.log_message(f"خطأ في قبول الاتصال: {str(e)}")
                        
        except Exception as e:
            self.log_message(f"خطأ في بدء الخادم: {str(e)}")
    
    def handle_client(self, client_socket, client_address):
        """التعامل مع العميل"""
        client_id = f"{client_address[0]}:{client_address[1]}"
        
        try:
            # إضافة العميل إلى القائمة
            self.clients[client_id] = {
                "socket": client_socket,
                "address": client_address,
                "connected_at": datetime.now(),
                "system_info": None
            }
            
            self.log_message(f"تم تسجيل العميل: {client_id}")
            
            while self.running:
                try:
                    # استقبال البيانات من العميل
                    data = self.receive_data(client_socket)
                    
                    if not data:
                        break
                    
                    # معالجة البيانات المستلمة
                    self.process_client_data(client_id, data)
                    
                except Exception as e:
                    self.log_message(f"خطأ مع العميل {client_id}: {str(e)}")
                    break
                    
        except Exception as e:
            self.log_message(f"خطأ في التعامل مع العميل {client_id}: {str(e)}")
        finally:
            # إزالة العميل من القائمة
            if client_id in self.clients:
                del self.clients[client_id]
            
            try:
                client_socket.close()
            except:
                pass
                
            self.log_message(f"انقطع الاتصال مع العميل: {client_id}")
    
    def receive_data(self, client_socket):
        """استقبال البيانات من العميل"""
        try:
            # استقبال طول الرسالة
            length_bytes = client_socket.recv(4)
            if not length_bytes:
                return None
                
            length = int.from_bytes(length_bytes, byteorder='big')
            
            # استقبال الرسالة
            message = b''
            while len(message) < length:
                chunk = client_socket.recv(length - len(message))
                if not chunk:
                    return None
                message += chunk
            
            # فك تشفير JSON
            json_data = message.decode('utf-8')
            return json.loads(json_data)
            
        except Exception as e:
            return None
    
    def send_data(self, client_socket, data):
        """إرسال البيانات إلى العميل"""
        try:
            json_data = json.dumps(data, ensure_ascii=False)
            message = json_data.encode('utf-8')
            
            # إرسال طول الرسالة أولاً
            length = len(message)
            client_socket.send(length.to_bytes(4, byteorder='big'))
            
            # إرسال الرسالة
            client_socket.send(message)
            return True
        except Exception as e:
            return False
    
    def process_client_data(self, client_id, data):
        """معالجة البيانات المستلمة من العميل"""
        data_type = data.get("type")
        
        if data_type == "system_info":
            # حفظ معلومات النظام
            self.clients[client_id]["system_info"] = data.get("data")
            self.log_message(f"تم استلام معلومات النظام من {client_id}")
            self.print_system_info(client_id, data.get("data"))
            
        elif data_type == "result":
            # نتيجة تنفيذ أمر
            result = data.get("data")
            self.log_message(f"نتيجة الأمر من {client_id}:")
            self.print_command_result(result)
            
        elif data_type == "file_result":
            # نتيجة عملية ملف
            result = data.get("data")
            self.log_message(f"نتيجة عملية الملف من {client_id}:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
        elif data_type == "pong":
            # رد على ping
            timestamp = data.get("timestamp")
            self.log_message(f"Pong من {client_id} في {timestamp}")
    
    def print_system_info(self, client_id, info):
        """طباعة معلومات النظام"""
        print(f"\n{'='*50}")
        print(f"معلومات النظام للعميل: {client_id}")
        print(f"{'='*50}")
        for key, value in info.items():
            print(f"{key}: {value}")
        print(f"{'='*50}\n")
    
    def print_command_result(self, result):
        """طباعة نتيجة الأمر"""
        print(f"\nالأمر: {result.get('command')}")
        print(f"رمز الإرجاع: {result.get('returncode')}")
        
        if result.get('stdout'):
            print("المخرجات:")
            print(result.get('stdout'))
        
        if result.get('stderr'):
            print("الأخطاء:")
            print(result.get('stderr'))
        
        if result.get('error'):
            print(f"خطأ: {result.get('error')}")
        
        print("-" * 50)
    
    def send_command_to_client(self, client_id, command):
        """إرسال أمر إلى عميل محدد"""
        if client_id in self.clients:
            client_socket = self.clients[client_id]["socket"]
            data = {"type": "execute", "command": command}
            
            if self.send_data(client_socket, data):
                self.log_message(f"تم إرسال الأمر إلى {client_id}: {command}")
                return True
            else:
                self.log_message(f"فشل في إرسال الأمر إلى {client_id}")
                return False
        else:
            self.log_message(f"العميل {client_id} غير متصل")
            return False
    
    def list_clients(self):
        """عرض قائمة العملاء المتصلين"""
        if not self.clients:
            print("لا يوجد عملاء متصلين")
            return
        
        print(f"\n{'='*60}")
        print("العملاء المتصلين:")
        print(f"{'='*60}")
        
        for client_id, info in self.clients.items():
            print(f"ID: {client_id}")
            print(f"متصل منذ: {info['connected_at'].strftime('%Y-%m-%d %H:%M:%S')}")
            
            if info['system_info']:
                sys_info = info['system_info']
                print(f"النظام: {sys_info.get('system')} {sys_info.get('release')}")
                print(f"المستخدم: {sys_info.get('username')}")
                print(f"اسم الجهاز: {sys_info.get('hostname')}")
            
            print("-" * 40)
        
        print(f"{'='*60}\n")
    
    def interactive_mode(self):
        """الوضع التفاعلي للتحكم"""
        print("\n" + "="*60)
        print("وضع التحكم التفاعلي")
        print("="*60)
        print("الأوامر المتاحة:")
        print("  list - عرض العملاء المتصلين")
        print("  select <client_id> - اختيار عميل للتحكم به")
        print("  cmd <command> - تنفيذ أمر على العميل المحدد")
        print("  file <operation> <path> - عمليات الملفات")
        print("  ping - اختبار الاتصال")
        print("  exit - الخروج")
        print("="*60)
        
        selected_client = None
        
        while self.running:
            try:
                if selected_client:
                    prompt = f"[{selected_client}]> "
                else:
                    prompt = "server> "
                
                user_input = input(prompt).strip()
                
                if not user_input:
                    continue
                
                parts = user_input.split(' ', 1)
                command = parts[0].lower()
                
                if command == "exit":
                    break
                elif command == "list":
                    self.list_clients()
                elif command == "select":
                    if len(parts) > 1:
                        client_id = parts[1]
                        if client_id in self.clients:
                            selected_client = client_id
                            print(f"تم اختيار العميل: {client_id}")
                        else:
                            print(f"العميل {client_id} غير موجود")
                    else:
                        print("استخدم: select <client_id>")
                elif command == "cmd":
                    if selected_client and len(parts) > 1:
                        cmd = parts[1]
                        self.send_command_to_client(selected_client, cmd)
                    else:
                        print("اختر عميل أولاً واكتب الأمر")
                elif command == "ping":
                    if selected_client:
                        client_socket = self.clients[selected_client]["socket"]
                        self.send_data(client_socket, {"type": "ping"})
                        print("تم إرسال ping")
                    else:
                        print("اختر عميل أولاً")
                else:
                    print("أمر غير معروف")
                    
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"خطأ: {str(e)}")
    
    def stop_server(self):
        """إيقاف الخادم"""
        self.running = False
        
        # قطع الاتصال مع جميع العملاء
        for client_id, info in self.clients.items():
            try:
                info["socket"].close()
            except:
                pass
        
        # إغلاق الخادم
        if self.server_socket:
            try:
                self.server_socket.close()
            except:
                pass
        
        self.log_message("تم إيقاف الخادم")

def main():
    print("=" * 60)
    print("برنامج التحكم عن بُعد - الخادم")
    print("Remote Control Server")
    print("=" * 60)
    
    server = RemoteControlServer()
    
    try:
        # بدء الخادم في thread منفصل
        server_thread = threading.Thread(target=server.start_server)
        server_thread.daemon = True
        server_thread.start()
        
        # انتظار قليل لبدء الخادم
        time.sleep(2)
        
        # بدء الوضع التفاعلي
        server.interactive_mode()
        
    except KeyboardInterrupt:
        print("\nتم إيقاف الخادم بواسطة المستخدم")
    except Exception as e:
        print(f"خطأ غير متوقع: {str(e)}")
    finally:
        server.stop_server()

if __name__ == "__main__":
    main()
