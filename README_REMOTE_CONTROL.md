# برنامج التحكم عن بُعد
## Remote Control System

### الوصف
برنامج للتحكم عن بُعد في الكمبيوتر من خارج المنزل باستخدام اتصال عكسي (Reverse Connection).

### الملفات
- `remote_control_client.py` - برنامج العميل (يعمل على الكمبيوتر المراد التحكم به)
- `remote_control_server.py` - برنامج الخادم (يعمل على الكمبيوتر المتحكم)
- `start_remote_client.bat` - تشغيل العميل
- `start_remote_server.bat` - تشغيل الخادم

### إعدادات الاتصال
- **LHOST**: **************
- **LPORT**: 16067

### كيفية الاستخدام

#### 1. تشغيل الخادم (على الكمبيوتر المتحكم)
```bash
python remote_control_server.py
```
أو استخدم:
```bash
start_remote_server.bat
```

#### 2. تشغيل العميل (على الكمبيوتر المراد التحكم به)
```bash
python remote_control_client.py
```
أو استخدم:
```bash
start_remote_client.bat
```

### الأوامر المتاحة في الخادم

#### أوامر عامة:
- `list` - عرض العملاء المتصلين
- `select <client_id>` - اختيار عميل للتحكم به
- `exit` - الخروج من البرنامج

#### أوامر التحكم (بعد اختيار عميل):
- `cmd <command>` - تنفيذ أمر على العميل
- `ping` - اختبار الاتصال

### أمثلة على الأوامر

#### عرض العملاء المتصلين:
```
server> list
```

#### اختيار عميل:
```
server> select *************:12345
```

#### تنفيذ أوامر:
```
[*************:12345]> cmd dir
[*************:12345]> cmd ipconfig
[*************:12345]> cmd systeminfo
[*************:12345]> cmd tasklist
```

#### أوامر مفيدة:
```bash
# عرض معلومات النظام
cmd systeminfo

# عرض العمليات الجارية
cmd tasklist

# عرض الشبكة
cmd ipconfig /all

# عرض المستخدمين
cmd net user

# عرض الخدمات
cmd net start

# إنشاء مجلد
cmd mkdir C:\temp

# نسخ ملف
cmd copy file1.txt file2.txt

# حذف ملف
cmd del filename.txt

# عرض محتويات مجلد
cmd dir C:\

# تشغيل برنامج
cmd notepad.exe

# إيقاف تشغيل الكمبيوتر (احذر!)
cmd shutdown /s /t 60

# إلغاء إيقاف التشغيل
cmd shutdown /a
```

### الميزات

#### العميل (Client):
- اتصال تلقائي بالخادم
- إعادة الاتصال التلقائي عند انقطاع الاتصال
- تنفيذ الأوامر مع إرجاع النتائج
- إرسال معلومات النظام
- دعم عمليات الملفات الأساسية

#### الخادم (Server):
- دعم عدة عملاء في نفس الوقت
- واجهة تفاعلية للتحكم
- عرض معلومات العملاء المتصلين
- تسجيل جميع العمليات مع الوقت

### الأمان
⚠️ **تحذير**: هذا البرنامج للأغراض التعليمية والاستخدام الشخصي فقط.

- تأكد من استخدام البرنامج على أجهزتك الشخصية فقط
- لا تستخدمه على أجهزة لا تملكها
- احرص على أمان الشبكة والاتصال

### متطلبات النظام
- Python 3.6 أو أحدث
- اتصال بالإنترنت
- فتح المنفذ 16067 في جدار الحماية (للخادم)

### استكشاف الأخطاء

#### مشاكل الاتصال:
1. تأكد من صحة عنوان IP والمنفذ
2. تأكد من فتح المنفذ في جدار الحماية
3. تأكد من وجود اتصال بالإنترنت

#### مشاكل الأوامر:
1. بعض الأوامر قد تحتاج صلاحيات إدارية
2. تأكد من صحة صيغة الأمر
3. بعض الأوامر قد تستغرق وقتاً طويلاً

### التطوير المستقبلي
- إضافة تشفير للاتصال
- واجهة رسومية
- نقل الملفات
- التحكم في سطح المكتب
- إدارة العمليات المتقدمة
