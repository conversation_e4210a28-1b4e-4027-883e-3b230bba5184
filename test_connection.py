#!/usr/bin/env python3
"""
اختبار الاتصال مع الخادم
"""
import socket
import time

def test_connection():
    LHOST = "**************"
    LPORT = 16067
    
    print("============================================================")
    print("اختبار الاتصال")
    print(f"LHOST: {LHOST}")
    print(f"LPORT: {LPORT}")
    print("============================================================")
    
    try:
        print("محاولة الاتصال...")
        s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        s.settimeout(10)
        
        result = s.connect_ex((LHOST, LPORT))
        
        if result == 0:
            print("✅ الاتصال نجح!")
            print("الخادم يعمل ويستمع على المنفذ")
            
            # إرسال رسالة اختبار
            test_msg = "TEST CONNECTION FROM PYTHON SCRIPT\n"
            s.send(test_msg.encode())
            
            # محاولة استقبال رد
            try:
                s.settimeout(5)
                response = s.recv(1024)
                if response:
                    print(f"رد من الخادم: {response.decode('utf-8', errors='ignore')}")
            except socket.timeout:
                print("لم يتم استقبال رد من الخادم (طبيعي)")
            
        else:
            print("❌ فشل الاتصال!")
            print("تأكد من:")
            print("1. تشغيل handler في Metasploit")
            print("2. صحة عنوان IP والمنفذ")
            print("3. عدم وجود firewall يمنع الاتصال")
            
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {str(e)}")
        print("تأكد من:")
        print("1. تشغيل Metasploit handler")
        print("2. الاتصال بالإنترنت")
        print("3. عدم حجب المنفذ")
        
    finally:
        try:
            s.close()
        except:
            pass
    
    print("============================================================")

if __name__ == "__main__":
    test_connection()
